let rbcData = [];
let redBloodCells = [];
let whiteBloodCells = [];
let yellowLeukocytes = [];

window.BLOOD_CELL_CONFIG = {
    COUNT: 120, // Reduced from 1200 to 120 to improve performance
    MIN_RADIUS: 0.06,
    MAX_RADIUS: 0.09,
    COLOR: "#ff4444",
    GLOW_COLOR: "#ff7777",
    MIN_DISTANCE: 0.5,
    MAX_DISTANCE: 5,
    SPEED_FACTOR: 1.1,
    WOBBLE_AMOUNT: 0.008,
    WOBBLE_SPEED: 1.2,
    WIDE_TUNNEL_MULTIPLIER: 2.5,
    WALL_GLOW_DISTANCE: 1.5,
    COLLISION_BOUNCE: 0.3,
    DAMAGE: 0,
    USE_PARTICLES: true
};

window.WHITE_BLOOD_CELL_CONFIG = {
    COUNT: 8,
    MIN_RADIUS: 0.15,
    MAX_RADIUS: 0.25,
    COLOR: "#ecf0f1",
    GLOW_COLOR: "#ffffff",
    ATTACK_COLOR: "#ff9900",
    MIN_DISTANCE: 10,
    MAX_DISTANCE: 30,
    DAMAGE: 5,
    HEALTH: 2,
    POINTS: 50,
    SPEED_FACTOR: 0.08,
    SPIKE_COUNT: 8,
    ATTACK_RANGE: 2.5,
    PROJECTILE_SPEED: 0.4,
    SLOW_EFFECT: 0.7
};

window.YELLOW_LEUKOCYTE_CONFIG = {
    COUNT: 4,
    MIN_RADIUS: 0.18,
    MAX_RADIUS: 0.28,
    COLOR: "#f1c40f",
    CORE_COLOR: "#e74c3c",
    GLOW_COLOR: "#f39c12",
    MIN_DISTANCE: 15,
    MAX_DISTANCE: 40,
    DAMAGE: 10,
    HEALTH: 3,
    POINTS: 100,
    SPIKE_COUNT: 8,
    SPEED_FACTOR: 0.1,
    ATTACK_RANGE: 3.0,
    PROJECTILE_SPEED: 0.5,
    HOMING_STRENGTH: 0.08,
    SCREEN_SHAKE: 0.3,
    REWARD_SHIELD_TIME: 5,
    WARNING_SOUND: "yellowLeukocyteWarning"
};

if (typeof window.textureCache === 'undefined') {
    window.textureCache = {};
}
const textureCache = window.textureCache;

const BLOOD_CELL_CONFIG = window.BLOOD_CELL_CONFIG;
const WHITE_BLOOD_CELL_CONFIG = window.WHITE_BLOOD_CELL_CONFIG;
const YELLOW_LEUKOCYTE_CONFIG = window.YELLOW_LEUKOCYTE_CONFIG;

async function loadTextureWithCaching(texturePath) {
    if (!scene) {
        return null;
    }

    if (textureCache[texturePath]) {
        return textureCache[texturePath];
    }

    return new Promise((resolve, reject) => {
        try {
            const isFileProtocol = window.location.protocol === 'file:';

            const initialPath = isFileProtocol && texturePath.startsWith('/') ?
                texturePath.substring(1) : texturePath;

            const texture = new BABYLON.Texture(
                initialPath,
                scene,
                false,
                false,
                BABYLON.Texture.BILINEAR_SAMPLINGMODE,
                () => {
                    textureCache[texturePath] = texture;
                    resolve(texture);
                },
                (error) => {
                    let alternativePath;
                    if (isFileProtocol) {
                        alternativePath = initialPath.startsWith('/') ?
                            initialPath.substring(1) : `/${initialPath}`;
                    } else {
                        alternativePath = texturePath.startsWith('/') ?
                            texturePath.substring(1) : `/${texturePath}`;
                    }

                    const fallbackTexture = new BABYLON.Texture(
                        alternativePath,
                        scene,
                        false,
                        false,
                        BABYLON.Texture.BILINEAR_SAMPLINGMODE,
                        () => {
                            textureCache[texturePath] = fallbackTexture;
                            resolve(fallbackTexture);
                        },
                        (fallbackError) => {
                            if (isFileProtocol) {
                                const lastResortPath = `textures/${texturePath.split('/').pop()}`;

                                const lastResortTexture = new BABYLON.Texture(
                                    lastResortPath,
                                    scene,
                                    false,
                                    false,
                                    BABYLON.Texture.BILINEAR_SAMPLINGMODE,
                                    () => {
                                        textureCache[texturePath] = lastResortTexture;
                                        resolve(lastResortTexture);
                                    },
                                    (lastError) => {
                                        const fallbackColoredTexture = BABYLON.Texture.CreateFromBase64String(
                                            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==",
                                            "fallback",
                                            scene
                                        );
                                        textureCache[texturePath] = fallbackColoredTexture;
                                        resolve(fallbackColoredTexture);
                                    }
                                );
                            } else {
                                const fallbackColoredTexture = BABYLON.Texture.CreateFromBase64String(
                                    "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==",
                                    "fallback",
                                    scene
                                );
                                textureCache[texturePath] = fallbackColoredTexture;
                                resolve(fallbackColoredTexture);
                            }
                        }
                    );
                }
            );
        } catch (e) {
            const fallbackColoredTexture = BABYLON.Texture.CreateFromBase64String(
                "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==",
                "fallback",
                scene
            );
            textureCache[texturePath] = fallbackColoredTexture;
            resolve(fallbackColoredTexture);
        }
    });
}

const bloodCellMeshes = [];
const bloodCellVelocities = [];

function clearBloodCells() {
    bloodCellMeshes.forEach(mesh => mesh.dispose());
    bloodCellMeshes.length = 0;
    bloodCellVelocities.length = 0;
    if (typeof clearOptimizedBloodCells === 'function') clearOptimizedBloodCells();
    if (BLOOD_CELL_CONFIG.USE_PARTICLES && window.bloodCellSystem) {
        window.bloodCellSystem.dispose();
        window.bloodCellSystem = null;
    }
    rbcData.length = 0;
    redBloodCells.length = 0;
    whiteBloodCells.length = 0;
    yellowLeukocytes.length = 0;
}

function createMovingRedBloodCells(numCells = 60) {
    bloodCellMeshes.forEach(mesh => mesh.dispose());
    bloodCellMeshes.length = 0;
    bloodCellVelocities.length = 0;
    redBloodCells.length = 0;
    for (let i = 0; i < numCells; i++) {
        // ...istniejąca logika tworzenia komórek...
    }
}

function updateMovingRedBloodCells(deltaTime) {
    if (!bunnyCollider) return;
    const zRange = BLOOD_CELL_CONFIG.MAX_DISTANCE * 2;

    if (!Array.isArray(window.redBloodCells)) {
        window.redBloodCells = [];

        if (bloodCellMeshes.length > 0 && window.redBloodCells.length === 0) {
            for (let i = 0; i < bloodCellMeshes.length; i++) {
                window.redBloodCells.push({
                    position: bloodCellMeshes[i].position.clone(),
                    mesh: bloodCellMeshes[i]
                });
            }
        }
    }

    for (let i = 0; i < bloodCellMeshes.length; i++) {
        const mesh = bloodCellMeshes[i];
        mesh.position.addInPlace(bloodCellVelocities[i].scale(deltaTime));
        mesh.position.y = mesh._baseY + Math.sin(performance.now() * 0.002 * mesh._wobbleSpeed + mesh._wobbleOffset) * 0.08;
        mesh.rotation.z += 0.01 * deltaTime;
        mesh.rotation.y += 0.008 * deltaTime;
        const dz = mesh.position.z - bunnyCollider.position.z;
        if (dz > zRange / 2) {
            const section = tunnelSections && tunnelSections.length > 0 ? tunnelSections[0] : null;
            const sectionRadius = section ? section.radius : 1.0;
            const sectionCenter = section && section.centerPoint ? section.centerPoint : {x: 0, y: 0};
            mesh.position.x = sectionCenter.x;
            mesh._baseY = sectionCenter.y;
            mesh.position.y = mesh._baseY;
            mesh.position.z = bunnyCollider.position.z + 15 + Math.random() * 10; // Zwiększono odległość z 5-10 na 15-25
        }

        if (i < window.redBloodCells.length) {
            window.redBloodCells[i].position.copyFrom(mesh.position);
        }
    }
}

function tryCreateBloodCellsWithParticles(scene, emitterPosition, tunnelRadius) {
    if (!scene) {
        return false;
    }

    if (!emitterPosition) {
        return false;
    }

    try {
        if (typeof createBloodCellParticleSystem === 'function') {
            const particleSystem = createBloodCellParticleSystem(scene, emitterPosition, tunnelRadius || 1.0);
            return particleSystem !== null;
        } else {
            const particleSystem = new BABYLON.ParticleSystem("bloodCells", 300, scene);
            particleSystem.particleTexture = new BABYLON.Texture("textures/blood_cell_soft.png", scene);
            particleSystem.emitter = emitterPosition;
            particleSystem.minEmitPower = 0.8;
            particleSystem.maxEmitPower = 1.5;
            particleSystem.minLifeTime = 3.0;
            particleSystem.maxLifeTime = 5.0;
            particleSystem.minSize = 0.1;
            particleSystem.maxSize = 0.2;
            particleSystem.emitRate = 100;
            particleSystem.blendMode = BABYLON.ParticleSystem.BLENDMODE_ADD;
            particleSystem.direction1 = new BABYLON.Vector3(0, 0, -1);
            particleSystem.direction2 = new BABYLON.Vector3(0, 0, -1);
            particleSystem.color1 = new BABYLON.Color4(1, 0, 0, 1.0);
            particleSystem.color2 = new BABYLON.Color4(1, 0.3, 0.3, 0.9);
            particleSystem.colorDead = new BABYLON.Color4(0.7, 0, 0, 0.3);
            particleSystem.gravity = new BABYLON.Vector3(0, 0, 0);
            particleSystem.minAngularSpeed = 0.1;
            particleSystem.maxAngularSpeed = 0.5;
            particleSystem.isBillboardBased = false; // Changed to false to make particles 3D instead of flat
            particleSystem.renderingGroupId = 1;
            particleSystem.start();

            window.bloodCellSystem = particleSystem;
            return true;
        }
    } catch (error) {
        return false;
    }
}

function updateRedBloodCells(deltaTime) {
    if (typeof updateOptimizedBloodCells === 'function') {
        updateOptimizedBloodCells(deltaTime);
    } else {
        updateMovingRedBloodCells(deltaTime);
        checkLaserRedBloodCellCollisions();
    }

    if (BLOOD_CELL_CONFIG.USE_PARTICLES && typeof updateBloodCellParticleEmitter === 'function') {
        updateBloodCellParticleEmitter();
    }
}

function checkLaserRedBloodCellCollisions() {
    if (!Array.isArray(lasers) || !Array.isArray(bloodCellMeshes)) return;

    for (let i = lasers.length - 1; i >= 0; i--) {
        const laser = lasers[i];
        if (!laser || !laser.mesh || laser.mesh.isDisposed()) continue;

        for (let j = 0; j < bloodCellMeshes.length; j++) {
            const bloodCell = bloodCellMeshes[j];
            if (!bloodCell) continue;

            const distance = BABYLON.Vector3.Distance(
                laser.mesh.position,
                bloodCell.position
            );

            // Increased collision threshold for better hit detection
            const collisionThreshold = 0.4;
            if (distance < collisionThreshold) {
                // Visual effect for hit
                if (typeof playParticleEffect === 'function') {
                    playParticleEffect("redBloodCellHit", bloodCell.position);
                }

                // Sound effect for hit
                if (typeof playSoundEffect === 'function') {
                    playSoundEffect(collectSound, 0.4);
                }

                // Add small score for hitting red blood cells
                if (typeof window.score !== 'undefined') {
                    window.score += 5;
                }

                // Respawn the blood cell at a new position
                if (bunnyCollider && bunnyCollider.position) {
                    const section = tunnelSections && tunnelSections.length > 0 ? tunnelSections[0] : null;
                    const sectionCenter = section && section.centerPoint ? section.centerPoint : {x: 0, y: 0};
                    bloodCell.position.x = sectionCenter.x;
                    bloodCell._baseY = sectionCenter.y;
                    bloodCell.position.y = bloodCell._baseY;
                    bloodCell.position.z = bunnyCollider.position.z + 15 + Math.random() * 10;

                    // Add some random velocity for more dynamic respawn
                    if (bloodCellVelocities && bloodCellVelocities[j]) {
                        bloodCellVelocities[j].x = (Math.random() - 0.5) * 0.02;
                        bloodCellVelocities[j].y = (Math.random() - 0.5) * 0.02;
                        bloodCellVelocities[j].z = -0.05 - Math.random() * 0.05;
                    }
                }

                // Remove the laser
                if (typeof poolManager !== 'undefined' && poolManager.returnObject) {
                    laser.mesh.setEnabled(false);
                    // Remove from glow layer if it exists
                    if (laser.glowLayer) {
                        laser.glowLayer.removeIncludedOnlyMesh(laser.mesh);
                    }
                    poolManager.returnObject("laser", laser);
                } else {
                    // Remove from glow layer if it exists
                    if (laser.glowLayer) {
                        laser.glowLayer.removeIncludedOnlyMesh(laser.mesh);
                    }
                    laser.mesh.dispose();
                }
                lasers.splice(i, 1);
                break;
            }
        }
    }
}

function generateRedBloodCells(numClouds = 12, cellsPerCloud = 80) {
    createMovingRedBloodCells(numClouds * cellsPerCloud);
}

function autoSpawnRedBloodCells(retryCount = 0) {
    const MAX_RETRIES = 10;
    const retryDelay = Math.min(1000 + retryCount * 200, 3000);

    if (!scene) {
        if (retryCount >= MAX_RETRIES) {
            return;
        }

        setTimeout(() => {
            autoSpawnRedBloodCells(retryCount + 1);
        }, retryDelay);
        return;
    }

    if (!bunnyCollider || !bunnyCollider.position) {
        if (retryCount >= MAX_RETRIES) {
            return;
        }

        setTimeout(() => {
            autoSpawnRedBloodCells(retryCount + 1);
        }, retryDelay);
        return;
    }

    // Check if demo mode is active - if so, skip blood cell spawning
    const isDemoMode = typeof window.isDemoActive === 'function' && window.isDemoActive();
    if (isDemoMode) {
        if (DEBUG_MODE) {
            console.log("Demo mode active: Skipping red blood cell auto-spawn");
        }
        return;
    }

    if (!Array.isArray(tunnelSections) || tunnelSections.length === 0) {
        if (retryCount >= MAX_RETRIES) {
            return;
        }

        setTimeout(() => {
            autoSpawnRedBloodCells(retryCount + 1);
        }, retryDelay);
        return;
    }

    if (!Array.isArray(window.redBloodCells)) {
        window.redBloodCells = [];
    }

    setTimeout(() => {
        if (typeof createOptimizedBloodCells === 'function') {
            createOptimizedBloodCells();
        } else {
            generateRedBloodCells();
        }

        const bloodCellCount = bloodCellMeshes ? bloodCellMeshes.length : 0;
        const redBloodCellCount = window.redBloodCells ? window.redBloodCells.length : 0;

        if (bloodCellCount < 100 || redBloodCellCount < 100) {
            setTimeout(() => {
                if (typeof createOptimizedBloodCells === 'function') {
                    createOptimizedBloodCells();
                } else {
                    generateRedBloodCells();
                }
            }, 1000);
        }

        if (BLOOD_CELL_CONFIG.USE_PARTICLES) {
            // Position the emitter closer to the player (within sight)
            const currentSection = tunnelSections[0]; // Get the current section
            const tunnelRadius = currentSection ? currentSection.radius : 1.0;

            // Position the emitter just ahead of the player (within sight)
            const emitterPosition = bunnyCollider.position.clone().add(new BABYLON.Vector3(0, 0, -5));

            // Adjust to tunnel center if available
            if (currentSection && currentSection.centerPoint) {
                emitterPosition.x = currentSection.centerPoint.x;
                emitterPosition.y = currentSection.centerPoint.y;
            }

            tryCreateBloodCellsWithParticles(scene, emitterPosition, tunnelRadius);
        }
    }, 500);
}

window.clearBloodCells = clearBloodCells;
window.autoSpawnRedBloodCells = autoSpawnRedBloodCells;

if (typeof clearBloodCells !== 'undefined') {
    globalThis.clearBloodCells = clearBloodCells;
}

if (typeof autoSpawnRedBloodCells !== 'undefined') {
    globalThis.autoSpawnRedBloodCells = autoSpawnRedBloodCells;
}

function createWhiteBloodCells() {
    if (!scene || !bunnyCollider || !tunnelSections || !tunnelSections.length === 0) {
        return;
    }
    if (Array.isArray(whiteBloodCells)) {
        whiteBloodCells.forEach(cell => {
            if (cell.mesh) cell.mesh.dispose();
            if (cell.light) cell.light.dispose();
        });
        whiteBloodCells.length = 0;
    }
    for (let i = 0; i < WHITE_BLOOD_CELL_CONFIG.COUNT; i++) {
        const sectionIndex = Math.floor(Math.random() * Math.min(tunnelSections.length, 20));
        const section = tunnelSections[sectionIndex];
        if (!section || !section.centerPoint) {
            continue;
        }
        const angle = Math.random() * Math.PI * 2;
        const radius = section.radius * (0.4 + Math.random() * 0.3);
        const zOffset = (Math.random() * 2 - 1) * section.radius * 0.3;
        const position = new BABYLON.Vector3(
            section.centerPoint.x + Math.cos(angle) * radius,
            section.centerPoint.y + Math.sin(angle) * radius,
            section.centerPoint.z + zOffset
        );
        const diameter = WHITE_BLOOD_CELL_CONFIG.MIN_RADIUS + Math.random() *
                        (WHITE_BLOOD_CELL_CONFIG.MAX_RADIUS - WHITE_BLOOD_CELL_CONFIG.MIN_RADIUS);
        const wbcMesh = BABYLON.MeshBuilder.CreateSphere(`whiteBloodCell_${i}`, {
            diameter: diameter * 2
        }, scene);
        wbcMesh.position = position;
        const wbcMaterial = new BABYLON.StandardMaterial(`wbcMat_${i}`, scene);
        wbcMaterial.diffuseColor = BABYLON.Color3.FromHexString(WHITE_BLOOD_CELL_CONFIG.COLOR);
        wbcMaterial.emissiveColor = BABYLON.Color3.FromHexString(WHITE_BLOOD_CELL_CONFIG.GLOW_COLOR).scale(0.5);
        wbcMaterial.specularColor = new BABYLON.Color3(1, 1, 1);
        wbcMaterial.specularPower = 32;
        wbcMesh.material = wbcMaterial;
        const wbcLight = new BABYLON.PointLight(`wbcLight_${i}`, position, scene);
        wbcLight.diffuse = BABYLON.Color3.FromHexString(WHITE_BLOOD_CELL_CONFIG.GLOW_COLOR);
        wbcLight.intensity = 1.0;
        wbcLight.range = 5;
        whiteBloodCells.push({
            mesh: wbcMesh,
            light: wbcLight,
            health: WHITE_BLOOD_CELL_CONFIG.HEALTH,
            attackTimer: 0,
            moveDirection: new BABYLON.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ).normalize(),
            created: Date.now()
        });
    }
}

function createYellowLeukocytes() {
    if (!scene || !bunnyCollider || !tunnelSections || tunnelSections.length === 0) {
        return;
    }
    if (Array.isArray(yellowLeukocytes)) {
        yellowLeukocytes.forEach(cell => {
            if (cell.mesh) cell.mesh.dispose();
            if (cell.light) cell.light.dispose();
        });
        yellowLeukocytes.length = 0;
    }
    for (let i = 0; i < YELLOW_LEUKOCYTE_CONFIG.COUNT; i++) {
        const sectionIndex = Math.floor(Math.random() * Math.min(tunnelSections.length / 2, 15));
        const section = tunnelSections[sectionIndex];
        if (!section || !section.centerPoint) {
            continue;
        }
        const angle = Math.random() * Math.PI * 2;
        const radius = section.radius * (0.3 + Math.random() * 0.3);
        const zOffset = (Math.random() * 2 - 1) * section.radius * 0.3;
        const position = new BABYLON.Vector3(
            section.centerPoint.x + Math.cos(angle) * radius,
            section.centerPoint.y + Math.sin(angle) * radius,
            section.centerPoint.z + zOffset
        );
        const diameter = YELLOW_LEUKOCYTE_CONFIG.MIN_RADIUS + Math.random() *
                        (YELLOW_LEUKOCYTE_CONFIG.MAX_RADIUS - YELLOW_LEUKOCYTE_CONFIG.MIN_RADIUS);
        const ylMesh = BABYLON.MeshBuilder.CreateSphere(`yellowLeukocyte_${i}`, {
            diameter: diameter * 2
        }, scene);
        ylMesh.position = position;
        const ylMaterial = new BABYLON.StandardMaterial(`ylMat_${i}`, scene);
        ylMaterial.diffuseColor = BABYLON.Color3.FromHexString(YELLOW_LEUKOCYTE_CONFIG.COLOR);
        ylMaterial.emissiveColor = BABYLON.Color3.FromHexString(YELLOW_LEUKOCYTE_CONFIG.GLOW_COLOR).scale(0.6);
        ylMaterial.specularColor = new BABYLON.Color3(1, 0.8, 0.2);
        ylMaterial.specularPower = 64;
        ylMesh.material = ylMaterial;
        const ylLight = new BABYLON.PointLight(`ylLight_${i}`, position, scene);
        ylLight.diffuse = BABYLON.Color3.FromHexString(YELLOW_LEUKOCYTE_CONFIG.GLOW_COLOR);
        ylLight.intensity = 1.2;
        ylLight.range = 6;
        yellowLeukocytes.push({
            mesh: ylMesh,
            light: ylLight,
            health: YELLOW_LEUKOCYTE_CONFIG.HEALTH,
            attackTimer: 0,
            moveDirection: new BABYLON.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ).normalize(),
            homingTarget: null,
            created: Date.now()
        });
    }
}

function updateWhiteBloodCells(deltaTime) {
    if (!bunnyCollider || !gameRunning) return;

    const playerPos = bunnyCollider.position;
    const moveSpeed = WHITE_BLOOD_CELL_CONFIG.SPEED_FACTOR * deltaTime * 100;

    for (let i = whiteBloodCells.length - 1; i >= 0; i--) {
        const wbc = whiteBloodCells[i];
        if (!wbc.mesh || wbc.mesh.isDisposed()) {
            whiteBloodCells.splice(i, 1);
            continue;
        }

        // Calculate distance to player
        const distanceToPlayer = BABYLON.Vector3.Distance(wbc.mesh.position, playerPos);

        // Predator behavior - different movement patterns based on distance
        if (distanceToPlayer < WHITE_BLOOD_CELL_CONFIG.ATTACK_RANGE) {
            // Aggressive pursuit when close to player
            const directionToPlayer = playerPos.subtract(wbc.mesh.position).normalize();

            // Predator-like behavior: more aggressive tracking when closer
            const trackingIntensity = Math.min(1.0, (WHITE_BLOOD_CELL_CONFIG.ATTACK_RANGE - distanceToPlayer) / WHITE_BLOOD_CELL_CONFIG.ATTACK_RANGE * 2);

            // Lerp with higher intensity when closer to player
            wbc.moveDirection = BABYLON.Vector3.Lerp(wbc.moveDirection, directionToPlayer, 0.1 + trackingIntensity * 0.3);
            wbc.moveDirection.normalize();

            // Increase speed when pursuing
            const pursuitSpeedMultiplier = 1.0 + trackingIntensity * 0.5;
            wbc.mesh.position.addInPlace(wbc.moveDirection.scale(moveSpeed * pursuitSpeedMultiplier));

            // Visual feedback - change color intensity based on aggression
            if (wbc.mesh.material) {
                wbc.mesh.material.emissiveColor = BABYLON.Color3.Lerp(
                    BABYLON.Color3.FromHexString(WHITE_BLOOD_CELL_CONFIG.GLOW_COLOR).scale(0.5),
                    BABYLON.Color3.FromHexString(WHITE_BLOOD_CELL_CONFIG.ATTACK_COLOR),
                    trackingIntensity
                );
            }

            // Add slight rotation for more dynamic movement
            wbc.mesh.rotation.y += deltaTime * (1.0 + trackingIntensity);
            wbc.mesh.rotation.x += deltaTime * trackingIntensity * 0.5;
        } 
        else if (distanceToPlayer < WHITE_BLOOD_CELL_CONFIG.ATTACK_RANGE * 2) {
            // Stalking behavior - move toward player but with some randomness
            const directionToPlayer = playerPos.subtract(wbc.mesh.position).normalize();

            // Add some randomness to movement (circling/stalking behavior)
            const randomOffset = new BABYLON.Vector3(
                (Math.random() - 0.5) * 0.4,
                (Math.random() - 0.5) * 0.4,
                (Math.random() - 0.5) * 0.2
            );

            // Combine direction to player with random movement
            const stalkDirection = directionToPlayer.add(randomOffset).normalize();

            // Gradually adjust direction (predator stalking)
            wbc.moveDirection = BABYLON.Vector3.Lerp(wbc.moveDirection, stalkDirection, 0.05);
            wbc.moveDirection.normalize();

            // Move at normal speed
            wbc.mesh.position.addInPlace(wbc.moveDirection.scale(moveSpeed));

            // Reset emissive color to normal
            if (wbc.mesh.material) {
                wbc.mesh.material.emissiveColor = BABYLON.Color3.FromHexString(WHITE_BLOOD_CELL_CONFIG.GLOW_COLOR).scale(0.5);
            }

            // Slower rotation when stalking
            wbc.mesh.rotation.y += deltaTime * 0.8;
        }
        else {
            // Wandering behavior when far from player
            // Occasionally change direction randomly
            if (Math.random() < 0.01) {
                const randomDir = new BABYLON.Vector3(
                    (Math.random() - 0.5) * 2,
                    (Math.random() - 0.5) * 2,
                    (Math.random() - 0.5) * 2
                ).normalize();

                wbc.moveDirection = BABYLON.Vector3.Lerp(wbc.moveDirection, randomDir, 0.3);
                wbc.moveDirection.normalize();
            }

            // Move at slightly reduced speed when wandering
            wbc.mesh.position.addInPlace(wbc.moveDirection.scale(moveSpeed * 0.8));

            // Gentle rotation when wandering
            wbc.mesh.rotation.y += deltaTime * 0.5;
        }

        // Ensure the cell stays within the tunnel
        if (typeof constrainPositionToTunnel === 'function') {
            wbc.mesh.position = constrainPositionToTunnel(wbc.mesh.position, 0.85);
        }
    }
}

function updateYellowLeukocytes(deltaTime) {
    if (!bunnyCollider || !gameRunning) return;

    const playerPos = bunnyCollider.position;
    const moveSpeed = YELLOW_LEUKOCYTE_CONFIG.SPEED_FACTOR * deltaTime * 100;

    for (let i = yellowLeukocytes.length - 1; i >= 0; i--) {
        const yl = yellowLeukocytes[i];
        if (!yl.mesh || yl.mesh.isDisposed()) {
            yellowLeukocytes.splice(i, 1);
            continue;
        }

        const distanceToPlayer = BABYLON.Vector3.Distance(yl.mesh.position, playerPos);

        // Advanced predator behavior for yellow leukocytes - more aggressive than white blood cells
        if (distanceToPlayer < YELLOW_LEUKOCYTE_CONFIG.ATTACK_RANGE) {
            // Aggressive hunting mode - direct pursuit with high tracking
            const directionToPlayer = playerPos.subtract(yl.mesh.position).normalize();

            // More aggressive tracking intensity than white blood cells
            const trackingIntensity = Math.min(1.0, (YELLOW_LEUKOCYTE_CONFIG.ATTACK_RANGE - distanceToPlayer) / YELLOW_LEUKOCYTE_CONFIG.ATTACK_RANGE * 2.5);

            // Higher homing strength when closer
            const adaptiveHomingStrength = YELLOW_LEUKOCYTE_CONFIG.HOMING_STRENGTH * (1 + trackingIntensity);

            // Lerp with higher intensity when closer to player
            yl.moveDirection = BABYLON.Vector3.Lerp(
                yl.moveDirection,
                directionToPlayer,
                adaptiveHomingStrength
            );
            yl.moveDirection.normalize();

            // Increase speed when pursuing - yellow leukocytes are faster in pursuit
            const pursuitSpeedMultiplier = 1.0 + trackingIntensity * 0.8;
            yl.mesh.position.addInPlace(yl.moveDirection.scale(moveSpeed * pursuitSpeedMultiplier));

            // Visual feedback - pulse effect when in attack mode
            if (yl.mesh.material) {
                // Pulsing effect based on sine wave
                const pulseIntensity = 0.5 + Math.sin(performance.now() * 0.01) * 0.5;
                yl.mesh.material.emissiveColor = BABYLON.Color3.Lerp(
                    BABYLON.Color3.FromHexString(YELLOW_LEUKOCYTE_CONFIG.GLOW_COLOR).scale(0.6),
                    BABYLON.Color3.FromHexString(YELLOW_LEUKOCYTE_CONFIG.CORE_COLOR),
                    pulseIntensity * trackingIntensity
                );
            }

            // Attack animation - faster rotation when pursuing
            yl.mesh.rotation.y += deltaTime * (2.5 + trackingIntensity);
            yl.mesh.rotation.x += deltaTime * (2.0 + trackingIntensity * 0.5);

            // Prepare to attack - increment attack timer
            yl.attackTimer = (yl.attackTimer || 0) + deltaTime;

            // Attack behavior - launch projectile or prepare special attack
            if (yl.attackTimer > 2.0) { // Attack every 2 seconds when in range
                yl.attackTimer = 0;

                // Visual attack indicator
                if (typeof playParticleEffect === 'function') {
                    playParticleEffect("yellowLeukocyteAttack", yl.mesh.position);
                }

                // Play warning sound if available
                if (typeof playSoundEffect === 'function' && YELLOW_LEUKOCYTE_CONFIG.WARNING_SOUND) {
                    playSoundEffect(YELLOW_LEUKOCYTE_CONFIG.WARNING_SOUND, 0.5);
                }
            }
        } 
        else if (distanceToPlayer < YELLOW_LEUKOCYTE_CONFIG.ATTACK_RANGE * 3) {
            // Hunting behavior - more sophisticated than white blood cells
            // Calculate a flanking position instead of direct approach
            const directionToPlayer = playerPos.subtract(yl.mesh.position).normalize();

            // Create a flanking vector perpendicular to direction to player
            const upVector = new BABYLON.Vector3(0, 1, 0);
            const flankingVector = BABYLON.Vector3.Cross(directionToPlayer, upVector).normalize();

            // Oscillate flanking direction based on time
            const flankingFactor = Math.sin(performance.now() * 0.001 + i) * 0.7;
            const flankingDirection = flankingVector.scale(flankingFactor);

            // Combine with direction to player for a circling/flanking behavior
            const huntingDirection = directionToPlayer.add(flankingDirection).normalize();

            // Gradually adjust direction (predator hunting)
            yl.moveDirection = BABYLON.Vector3.Lerp(yl.moveDirection, huntingDirection, 0.08);
            yl.moveDirection.normalize();

            // Move at slightly increased speed when hunting
            yl.mesh.position.addInPlace(yl.moveDirection.scale(moveSpeed * 1.2));

            // Reset attack timer when not in direct attack range
            yl.attackTimer = 0;

            // Normal appearance when hunting
            if (yl.mesh.material) {
                yl.mesh.material.emissiveColor = BABYLON.Color3.FromHexString(YELLOW_LEUKOCYTE_CONFIG.GLOW_COLOR).scale(0.6);
            }

            // Moderate rotation when hunting
            yl.mesh.rotation.y += deltaTime * 2.0;
            yl.mesh.rotation.x += deltaTime * 1.5;
        }
        else {
            // Patrolling behavior when far from player
            // Occasionally change direction with purpose
            if (Math.random() < 0.02) {
                // Create a more purposeful random direction that tends toward player's general direction
                const randomDir = new BABYLON.Vector3(
                    (Math.random() - 0.5) * 2,
                    (Math.random() - 0.5) * 2,
                    (Math.random() - 0.5) * 2
                ).normalize();

                // Slightly bias toward player's direction even when far away (predator instinct)
                const playerDirection = playerPos.subtract(yl.mesh.position).normalize().scale(0.3);
                const biasedDirection = randomDir.add(playerDirection).normalize();

                yl.moveDirection = BABYLON.Vector3.Lerp(yl.moveDirection, biasedDirection, 0.4);
                yl.moveDirection.normalize();
            }

            // Move at normal speed when patrolling
            yl.mesh.position.addInPlace(yl.moveDirection.scale(moveSpeed));

            // Reset attack timer when not in range
            yl.attackTimer = 0;

            // Gentle rotation when patrolling
            yl.mesh.rotation.y += deltaTime * 1.5;
            yl.mesh.rotation.x += deltaTime * 1.0;
        }

        // Ensure the cell stays within the tunnel
        if (typeof constrainPositionToTunnel === 'function') {
            yl.mesh.position = constrainPositionToTunnel(yl.mesh.position, 0.85);
        }
    }
}

function checkLaserWhiteBloodCellCollisions() {
    if (!Array.isArray(lasers) || !Array.isArray(whiteBloodCells)) return;

    for (let i = lasers.length - 1; i >= 0; i--) {
        const laser = lasers[i];
        if (!laser || !laser.mesh || laser.mesh.isDisposed()) continue;

        for (let j = whiteBloodCells.length - 1; j >= 0; j--) {
            const wbc = whiteBloodCells[j];
            if (!wbc.mesh || wbc.mesh.isDisposed()) continue;

            const distance = BABYLON.Vector3.Distance(laser.mesh.position, wbc.mesh.position);
            if (distance < 0.8) {
                wbc.health--;

                if (wbc.health <= 0) {
                    if (typeof playParticleEffect === 'function') {
                        playParticleEffect("whiteBloodCellDestruction", wbc.mesh.position);
                    }
                    if (typeof playSoundEffect === 'function') {
                        playSoundEffect(collectSound, 0.7);
                    }

                    score += WHITE_BLOOD_CELL_CONFIG.POINTS;

                    wbc.light?.dispose();
                    wbc.mesh.dispose();
                    whiteBloodCells.splice(j, 1);
                } else {
                    if (wbc.mesh.material && wbc.mesh.material.emissiveColor) {
                        wbc.mesh.material.emissiveColor = new BABYLON.Color3(1, 0.2, 0.2);
                    }
                }

                if (typeof poolManager !== 'undefined' && poolManager.returnObject) {
                    laser.mesh.setEnabled(false);
                    // Usuń z glow layer jeśli istnieje
                    if (laser.glowLayer) {
                        laser.glowLayer.removeIncludedOnlyMesh(laser.mesh);
                    }
                    poolManager.returnObject("laser", laser);
                } else {
                    // Usuń z glow layer jeśli istnieje
                    if (laser.glowLayer) {
                        laser.glowLayer.removeIncludedOnlyMesh(laser.mesh);
                    }
                    laser.mesh.dispose();
                }
                lasers.splice(i, 1);
                break;
            }
        }
    }
}

function checkLaserYellowLeukocyteCollisions() {
    if (!Array.isArray(lasers) || !Array.isArray(yellowLeukocytes)) return;

    for (let i = lasers.length - 1; i >= 0; i--) {
        const laser = lasers[i];
        if (!laser || !laser.mesh || laser.mesh.isDisposed()) continue;

        for (let j = yellowLeukocytes.length - 1; j >= 0; j--) {
            const yl = yellowLeukocytes[j];
            if (!yl.mesh || yl.mesh.isDisposed()) continue;

            const distance = BABYLON.Vector3.Distance(laser.mesh.position, yl.mesh.position);
            if (distance < 1.0) {
                yl.health--;

                if (yl.health <= 0) {
                    if (typeof playParticleEffect === 'function') {
                        playParticleEffect("yellowLeukocyteDestruction", yl.mesh.position);
                    }
                    if (typeof playSoundEffect === 'function') {
                        playSoundEffect(collectSound, 0.8);
                    }

                    score += YELLOW_LEUKOCYTE_CONFIG.POINTS;

                    yl.light?.dispose();
                    yl.mesh.dispose();
                    yellowLeukocytes.splice(j, 1);
                } else {
                    if (yl.mesh.material && yl.mesh.material.emissiveColor) {
                        yl.mesh.material.emissiveColor = new BABYLON.Color3(1, 0.5, 0);
                    }
                    yl.moveDirection = bunnyCollider.position.subtract(yl.mesh.position).normalize();
                }

                if (typeof poolManager !== 'undefined' && poolManager.returnObject) {
                    laser.mesh.setEnabled(false);
                    // Usuń z glow layer jeśli istnieje
                    if (laser.glowLayer) {
                        laser.glowLayer.removeIncludedOnlyMesh(laser.mesh);
                    }
                    poolManager.returnObject("laser", laser);
                } else {
                    // Usuń z glow layer jeśli istnieje
                    if (laser.glowLayer) {
                        laser.glowLayer.removeIncludedOnlyMesh(laser.mesh);
                    }
                    laser.mesh.dispose();
                }
                lasers.splice(i, 1);
                break;
            }
        }
    }
}

function createRedBloodCellsAtCurrentSegment() {
    if (!bunnyCollider || !tunnelSections || tunnelSections.length === 0 || !scene) {
        return;
    }

    const currentSection = findSectionAtPosition(bunnyCollider.position.z);
    if (!currentSection) return;

    const segmentsAhead = 3;
    const cellsPerSegment = 5;

    for (let segOffset = 1; segOffset <= segmentsAhead; segOffset++) {
        const targetSectionIndex = Math.max(0, currentSection.index - segOffset);
        const targetSection = tunnelSections[targetSectionIndex];

        if (!targetSection) continue;

        for (let i = 0; i < cellsPerSegment; i++) {
            const angle = Math.random() * Math.PI * 2;
            const radius = targetSection.radius * Math.sqrt(Math.random()) * 0.8;

            const segmentLength = BABYLON.Vector3.Distance(targetSection.startPoint, targetSection.endPoint);
            const zProgress = Math.random();
            const zOffset = segmentLength * zProgress;

            const position = new BABYLON.Vector3(
                targetSection.centerPoint.x + Math.cos(angle) * radius,
                targetSection.centerPoint.y + Math.sin(angle) * radius,
                targetSection.startPoint.z + (targetSection.endPoint.z - targetSection.startPoint.z) * zProgress
            );

            if (!window.redBloodCells) window.redBloodCells = [];

            if (window.redBloodCells.length < 100) {
                const cell = {
                    position: position.clone(),
                    velocity: new BABYLON.Vector3(0, 0, 0),
                    age: 0,
                    maxAge: 30 + Math.random() * 20
                };

                if (typeof createRedBloodCellMesh === 'function') {
                    cell.mesh = createRedBloodCellMesh(position, targetSection.radius * 0.05);
                }

                window.redBloodCells.push(cell);
            }
        }
    }
}

function updateRedBloodCellsEnhanced(deltaTime) {
    if (!Array.isArray(window.redBloodCells) || !bunnyCollider) return;

    let playerSpeed = Config.BASE_SPEED * (currentSpeedMultiplier || 1.0);
    if (typeof getPlayerSpeed === 'function') {
        playerSpeed = getPlayerSpeed();
    }

    for (let i = window.redBloodCells.length - 1; i >= 0; i--) {
        const cell = window.redBloodCells[i];

        cell.age += deltaTime;

        if (cell.age > cell.maxAge) {
            if (cell.mesh) {
                cell.mesh.dispose();
            }
            window.redBloodCells.splice(i, 1);
            continue;
        }

        const cellSpeed = playerSpeed * (0.9 + Math.random() * 0.3);
        cell.position.z -= cellSpeed * deltaTime;

        const driftSpeed = 0.5 * deltaTime;
        cell.position.x += Math.sin(performance.now() / 1000 + i) * driftSpeed;
        cell.position.y += Math.cos(performance.now() / 800 + i) * driftSpeed;

        if (cell.position.z < bunnyCollider.position.z - 15) {
            if (cell.mesh) {
                cell.mesh.dispose();
            }
            window.redBloodCells.splice(i, 1);
            continue;
        }

        if (cell.mesh && !cell.mesh.isDisposed()) {
            cell.mesh.position.copyFrom(cell.position);
        }
    }

    if (!window.lastBloodCellCreation) window.lastBloodCellCreation = 0;
    if (Date.now() - window.lastBloodCellCreation > 2000) {
        createRedBloodCellsAtCurrentSegment();
        window.lastBloodCellCreation = Date.now();
    }
}

window.createWhiteBloodCells = createWhiteBloodCells;
window.createYellowLeukocytes = createYellowLeukocytes;
window.updateWhiteBloodCells = updateWhiteBloodCells;
window.updateYellowLeukocytes = updateYellowLeukocytes;
window.checkLaserWhiteBloodCellCollisions = checkLaserWhiteBloodCellCollisions;
window.checkLaserYellowLeukocyteCollisions = checkLaserYellowLeukocyteCollisions;
window.createRedBloodCellsAtCurrentSegment = createRedBloodCellsAtCurrentSegment;
window.updateRedBloodCellsEnhanced = updateRedBloodCellsEnhanced;

class BloodCellOptimizer {
    constructor() {
        this.config = {
            maxRedBloodCells: 150,
            maxWhiteBloodCells: 30,
            maxYellowLeukocytes: 20,
            cleanupInterval: 3000,
            performanceCheckInterval: 5000,
            lowFpsThreshold: 30
        };

        this.lastCleanup = 0;
        this.lastPerformanceCheck = 0;
        this.performanceMode = false;
        this.stats = {
            cellsRemoved: 0,
            cleanupCount: 0,
            lastFps: 0
        };
    }

    initialize() {
        // Initial cleanup to ensure we start with a clean state
        this.cleanup(true);

        // Set up periodic cleanup
        if (typeof window !== 'undefined') {
            setInterval(() => this.cleanup(), this.config.cleanupInterval);
            setInterval(() => this.checkPerformance(), this.config.performanceCheckInterval);
        }

        console.log("BloodCellOptimizer initialized");
    }

    cleanup(force = false) {
        const now = performance.now();
        if (!force && now - this.lastCleanup < this.config.cleanupInterval) return;

        this.lastCleanup = now;
        let totalRemoved = 0;

        // Cleanup red blood cells
        if (Array.isArray(window.redBloodCells)) {
            const maxCells = this.performanceMode ?
                Math.floor(this.config.maxRedBloodCells * 0.5) :
                this.config.maxRedBloodCells;

            if (window.redBloodCells.length > maxCells) {
                const toRemove = window.redBloodCells.length - maxCells;
                for (let i = 0; i < toRemove; i++) {
                    const cell = window.redBloodCells.shift();
                    if (cell && cell.mesh && !cell.mesh.isDisposed()) {
                        cell.mesh.dispose();
                    }
                }
                totalRemoved += toRemove;
            }
        }

        // Cleanup white blood cells
        if (Array.isArray(window.whiteBloodCells)) {
            const maxCells = this.performanceMode ?
                Math.floor(this.config.maxWhiteBloodCells * 0.5) :
                this.config.maxWhiteBloodCells;

            if (window.whiteBloodCells.length > maxCells) {
                const toRemove = window.whiteBloodCells.length - maxCells;
                for (let i = 0; i < toRemove; i++) {
                    const cell = window.whiteBloodCells.shift();
                    if (cell && cell.mesh && !cell.mesh.isDisposed()) {
                        cell.mesh.dispose();
                    }
                }
                totalRemoved += toRemove;
            }
        }

        // Cleanup yellow leukocytes
        if (Array.isArray(window.yellowLeukocytes)) {
            const maxCells = this.performanceMode ?
                Math.floor(this.config.maxYellowLeukocytes * 0.5) :
                this.config.maxYellowLeukocytes;

            if (window.yellowLeukocytes.length > maxCells) {
                const toRemove = window.yellowLeukocytes.length - maxCells;
                for (let i = 0; i < toRemove; i++) {
                    const cell = window.yellowLeukocytes.shift();
                    if (cell && cell.mesh && !cell.mesh.isDisposed()) {
                        cell.mesh.dispose();
                    }
                }
                totalRemoved += toRemove;
            }
        }

        // Update stats
        if (totalRemoved > 0) {
            this.stats.cellsRemoved += totalRemoved;
            this.stats.cleanupCount++;

            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
                console.log(`BloodCellOptimizer: Removed ${totalRemoved} cells (Performance mode: ${this.performanceMode})`);
            }
        }
    }

    checkPerformance() {
        const now = performance.now();
        if (now - this.lastPerformanceCheck < this.config.performanceCheckInterval) return;

        this.lastPerformanceCheck = now;

        // Check FPS if engine is available
        if (window.engine && typeof window.engine.getFps === 'function') {
            const fps = window.engine.getFps();
            this.stats.lastFps = fps;

            // Toggle performance mode based on FPS
            const newPerformanceMode = fps < this.config.lowFpsThreshold;

            if (newPerformanceMode !== this.performanceMode) {
                this.performanceMode = newPerformanceMode;

                if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
                    console.log(`BloodCellOptimizer: Performance mode ${this.performanceMode ? 'enabled' : 'disabled'} (FPS: ${fps.toFixed(1)})`);
                }

                // Immediate cleanup if entering performance mode
                if (this.performanceMode) {
                    this.cleanup(true);
                }
            }
        }
    }

    getStats() {
        return {
            ...this.stats,
            redBloodCells: window.redBloodCells?.length || 0,
            whiteBloodCells: window.whiteBloodCells?.length || 0,
            yellowLeukocytes: window.yellowLeukocytes?.length || 0,
            performanceMode: this.performanceMode
        };
    }
}

// Create global instance
const bloodCellOptimizer = new BloodCellOptimizer();

// Initialize on load
if (typeof window !== 'undefined') {
    bloodCellOptimizer.initialize();
}

// Patch the update function to include optimization
const originalUpdateRedBloodCells = window.updateRedBloodCells;
if (typeof originalUpdateRedBloodCells === 'function') {
    window.updateRedBloodCells = function(deltaTime) {
        try {
            // Call original function
            originalUpdateRedBloodCells(deltaTime);

            // Run optimization if needed
            bloodCellOptimizer.checkPerformance();
        } catch (error) {
            console.error("Error in updateRedBloodCells:", error);
        }
    };
}

// Export for global access
window.bloodCellOptimizer = bloodCellOptimizer;

let redBloodCellCount = 0;
let updateCounter = 0;

function createOptimizedBloodCells() {
    if (!scene || !bunnyCollider) return;

    // Check if demo mode is active - if so, skip blood cell creation
    const isDemoMode = typeof window.isDemoActive === 'function' && window.isDemoActive();
    if (isDemoMode) {
        if (DEBUG_MODE) {
            console.log("Demo mode active: Skipping optimized blood cell creation");
        }
        return;
    }

    clearOptimizedBloodCells();

    if (!tunnelSections || tunnelSections.length === 0) {
        console.warn("No tunnel sections available for blood cell emitter");
        return;
    }

    if (DEBUG_MODE) console.log(`Creating optimized blood cells using particle system`);

    if (window.BLOOD_CELL_CONFIG && window.BLOOD_CELL_CONFIG.USE_PARTICLES) {
        const playerSection = findSectionAtPosition(bunnyCollider.position.z);

        // Position the emitter closer to the player (within sight)
        // Use the current player section instead of one far away
        const emitterSection = playerSection;

        // Create emitter position just ahead of the player (within sight)
        const emitterPosition = bunnyCollider.position.clone().add(new BABYLON.Vector3(0, 0, -5));

        // Adjust to tunnel center if available
        if (emitterSection && emitterSection.centerPoint) {
            emitterPosition.x = emitterSection.centerPoint.x;
            emitterPosition.y = emitterSection.centerPoint.y;
        }

        const tunnelRadius = emitterSection.radius;

        if (typeof createBloodCellParticleSystem === 'function') {
            const particleSystem = createBloodCellParticleSystem(scene, emitterPosition, tunnelRadius);
            if (particleSystem) {
                particleSystem.minLifeTime = 15.0;
                particleSystem.maxLifeTime = 20.0;
                particleSystem.emitRate = 80;

                const directionAlongTunnel = tunnelDirection.clone();
                particleSystem.direction1 = directionAlongTunnel.add(new BABYLON.Vector3(-0.2, -0.2, -0.2));
                particleSystem.direction2 = directionAlongTunnel.add(new BABYLON.Vector3(0.2, 0.2, 0.2));

                const playerSpeed = Config.BASE_SPEED * (currentSpeedMultiplier || 1.0);
                particleSystem.minEmitPower = playerSpeed * 1.1;
                particleSystem.maxEmitPower = playerSpeed * 1.3;

                particleSystem.minSize = 0.08;
                particleSystem.maxSize = 0.15;

                particleSystem.color1 = new BABYLON.Color4(1, 0.2, 0.2, 0.9);
                particleSystem.color2 = new BABYLON.Color4(0.8, 0.1, 0.1, 0.8);
                particleSystem.colorDead = new BABYLON.Color4(0.5, 0.1, 0.1, 0.3);

                window.bloodCellSystem = particleSystem;
                window.bloodCellEmitterPosition = emitterPosition.clone();

                if (DEBUG_MODE) console.log("[bloodCellsOptimized] Created particle system at farthest tunnel section");
            }
        } else if (typeof tryCreateBloodCellsWithParticles === 'function') {
            const result = tryCreateBloodCellsWithParticles(scene, emitterPosition, tunnelRadius);
            if (result) {
                window.bloodCellEmitterPosition = emitterPosition.clone();
            }
            if (DEBUG_MODE) console.log("[bloodCellsOptimized] Created particle system via fallback: " + (result ? "success" : "failure"));
        } else {
            if (DEBUG_MODE) console.warn("[bloodCellsOptimized] No particle system creation functions available");
        }
    } else {
        if (DEBUG_MODE) console.warn("[bloodCellsOptimized] Particle system disabled in configuration");
    }
}

function updateOptimizedBloodCells(deltaTime) {
    if (!scene || !bunnyCollider) return;

    if (window.BLOOD_CELL_CONFIG && window.BLOOD_CELL_CONFIG.USE_PARTICLES && window.bloodCellSystem) {
        const currentSection = findSectionAtPosition(bunnyCollider.position.z);
        if (currentSection && tunnelSections && tunnelSections.length > 0) {
            const playerSectionIndex = currentSection.index;
            const lookAheadDistance = Math.min(15, Math.floor(tunnelSections.length * 0.1));
            const targetSectionIndex = Math.max(0, playerSectionIndex - lookAheadDistance);
            const targetSection = tunnelSections[targetSectionIndex];

            if (targetSection) {
                const newEmitterPosition = targetSection.startPoint.clone();

                const tunnelDirection = targetSection.endPoint.subtract(targetSection.startPoint).normalize();
                newEmitterPosition.subtractInPlace(tunnelDirection.scale(3));

                if (targetSection.centerPoint) {
                    newEmitterPosition.x = targetSection.centerPoint.x;
                    newEmitterPosition.y = targetSection.centerPoint.y;
                }

                if (!window.bloodCellEmitterPosition ||
                    BABYLON.Vector3.Distance(window.bloodCellEmitterPosition, newEmitterPosition) > 5) {

                    window.bloodCellSystem.emitter = newEmitterPosition;
                    window.bloodCellEmitterPosition = newEmitterPosition.clone();

                    const directionToPlayer = bunnyCollider.position.subtract(newEmitterPosition).normalize();
                    window.bloodCellSystem.direction1 = directionToPlayer.add(new BABYLON.Vector3(-0.3, -0.3, -0.1));
                    window.bloodCellSystem.direction2 = directionToPlayer.add(new BABYLON.Vector3(0.3, 0.3, 0.1));

                    const playerSpeed = Config.BASE_SPEED * (currentSpeedMultiplier || 1.0);
                    window.bloodCellSystem.minEmitPower = playerSpeed * 1.1;
                    window.bloodCellSystem.maxEmitPower = playerSpeed * 1.3;

                }
            }
        }

        if (typeof updateBloodCellParticleEmitter === 'function') {
            updateBloodCellParticleEmitter();
        }
    }
}

function clearOptimizedBloodCells() {
    if (window.BLOOD_CELL_CONFIG && window.BLOOD_CELL_CONFIG.USE_PARTICLES && window.bloodCellSystem) {
        window.bloodCellSystem.dispose();
        window.bloodCellSystem = null;
    } else if (window.bloodCellSystem) {
        window.bloodCellSystem.dispose();
        window.bloodCellSystem = null;
    }

    redBloodCellCount = 0;
    updateCounter = 0;
}

function updateBloodCellsWithConstraints(deltaTime) {
    if (!bunnyCollider || !scene || !gameRunning) return;

    if (typeof updateOptimizedBloodCells === 'function') {
        updateOptimizedBloodCells(deltaTime);
    } else if (typeof updateRedBloodCells === 'function') {
        updateRedBloodCells(deltaTime);

        if (redBloodCells && redBloodCells.length > 0) {
            redBloodCells.forEach(cell => {
                if (cell.mesh && !cell.mesh.isDisposed()) {
                    if (!isPositionInTunnel(cell.position)) {
                        positionBloodCellRandomly(cell.position);
                        cell.mesh.position.copyFrom(cell.position);
                    }
                }
            });
        }
    }

    if (typeof updateWhiteBloodCells === 'function') {
        updateWhiteBloodCells(deltaTime);

        if (whiteBloodCells && whiteBloodCells.length > 0) {
            whiteBloodCells.forEach(wbc => {
                if (wbc.mesh && !wbc.mesh.isDisposed()) {
                    if (!isPositionInTunnel(wbc.mesh.position)) {
                        wbc.mesh.position = constrainPositionToTunnel(wbc.mesh.position, 0.85);
                    }
                }
            });
        }
    }
}
